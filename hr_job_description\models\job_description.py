# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class JobDescription(models.Model):
    _name = 'hr.job.description'
    _description = 'Job Description'
    _order = 'employee_id, approval_date desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # Basic Information
    name = fields.Char(
        string='Job Description Title',
        required=True,
        tracking=True,
        help='Title for this job description'
    )

    employee_id = fields.Many2one(
        'hr.employee',
        string='Employee',
        required=True,
        tracking=True,
        help='Employee for whom this job description is created'
    )

    job_title = fields.Char(
        string='Job Title',
        required=True,
        tracking=True,
        help='Official job title for this position'
    )

    department_id = fields.Many2one(
        'hr.department',
        string='Department',
        required=True,
        tracking=True,
        help='Department where this position belongs'
    )

    # Job Description Content
    job_description = fields.Html(
        string='Job Description',
        help='Detailed description of the job position'
    )

    main_tasks = fields.Html(
        string='Main Tasks',
        help='Primary tasks and duties of this position'
    )

    responsibilities = fields.Html(
        string='Responsibilities',
        help='Key responsibilities and accountabilities'
    )

    required_skills = fields.Html(
        string='Required Skills',
        help='Technical and soft skills required for this position'
    )

    required_qualifications = fields.Html(
        string='Required Qualifications',
        help='Educational qualifications and certifications required'
    )

    required_experience = fields.Html(
        string='Required Experience',
        help='Work experience requirements for this position'
    )

    # Status and Dates
    approval_date = fields.Date(
        string='Approval Date',
        default=fields.Date.context_today,
        tracking=True,
        help='Date when this job description was approved'
    )

    state = fields.Selection([
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ], string='Status', default='active', required=True, tracking=True,
       help='Current status of this job description')

    # Additional Information
    notes = fields.Text(
        string='Additional Notes',
        help='Any additional information or notes'
    )

    # Computed Fields
    employee_name = fields.Char(
        related='employee_id.name',
        string='Employee Name',
        store=True,
        readonly=True
    )

    department_name = fields.Char(
        related='department_id.name',
        string='Department Name',
        store=True,
        readonly=True
    )

    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    @api.model
    def create(self, vals):
        """Override create to set default name if not provided"""
        if not vals.get('name'):
            employee = self.env['hr.employee'].browse(vals.get('employee_id'))
            if employee:
                vals['name'] = _('Job Description - %s') % employee.name
        return super(JobDescription, self).create(vals)

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        """Auto-fill job title and department from employee"""
        if self.employee_id:
            self.job_title = self.employee_id.job_title or self.employee_id.job_id.name
            self.department_id = self.employee_id.department_id
            if not self.name:
                self.name = _('Job Description - %s') % self.employee_id.name

    @api.constrains('employee_id', 'state')
    def _check_active_job_description(self):
        """Ensure only one active job description per employee"""
        for record in self:
            if record.state == 'active':
                existing = self.search([
                    ('employee_id', '=', record.employee_id.id),
                    ('state', '=', 'active'),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(_(
                        'Employee %s already has an active job description. '
                        'Please deactivate the existing one before creating a new active one.'
                    ) % record.employee_id.name)

    def action_activate(self):
        """Activate this job description"""
        self.ensure_one()
        # Deactivate other active job descriptions for this employee
        other_active = self.search([
            ('employee_id', '=', self.employee_id.id),
            ('state', '=', 'active'),
            ('id', '!=', self.id)
        ])
        other_active.write({'state': 'inactive'})
        self.write({'state': 'active'})
        return True

    def action_deactivate(self):
        """Deactivate this job description"""
        self.ensure_one()
        self.write({'state': 'inactive'})
        return True

    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"{record.employee_name} - {record.job_title}"
            if record.state == 'inactive':
                name += _(' (Inactive)')
            result.append((record.id, name))
        return result

    @api.model
    def _cron_approval_reminder(self):
        """Cron job to remind about job descriptions without approval date"""
        from datetime import datetime, timedelta

        # Find job descriptions older than 30 days without approval date
        thirty_days_ago = datetime.now() - timedelta(days=30)
        job_descriptions = self.search([
            ('approval_date', '=', False),
            ('create_date', '<', thirty_days_ago),
            ('state', '=', 'active')
        ])

        for job_desc in job_descriptions:
            if job_desc.employee_id.parent_id and job_desc.employee_id.parent_id.user_id:
                job_desc.activity_schedule(
                    'mail.mail_activity_data_todo',
                    summary=_('Job Description Approval Required'),
                    note=_('Please review and approve the job description for %s') % job_desc.employee_id.name,
                    user_id=job_desc.employee_id.parent_id.user_id.id
                )
