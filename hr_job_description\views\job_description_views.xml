<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Job Description Form View -->
        <record id="view_job_description_form" model="ir.ui.view">
            <field name="name">hr.job.description.form</field>
            <field name="model">hr.job.description</field>
            <field name="arch" type="xml">
                <form string="Job Description">
                    <header>
                        <button name="action_activate" string="Activate" type="object"
                                class="btn-primary" attrs="{'invisible': [('state', '=', 'active')]}"/>
                        <button name="action_deactivate" string="Deactivate" type="object"
                                class="btn-secondary" attrs="{'invisible': [('state', '=', 'inactive')]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="active,inactive"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="%(hr_job_description.action_job_description_report)d" type="action"
                                    class="oe_stat_button" icon="fa-print" string="Print PDF"/>
                        </div>

                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Job Description Title"/>
                            </h1>
                        </div>

                        <group>
                            <group string="Basic Information">
                                <field name="employee_id" options="{'no_create': True}"/>
                                <field name="job_title" placeholder="e.g. Senior Software Developer"/>
                                <field name="department_id" options="{'no_create': True}"/>
                                <field name="approval_date"/>
                            </group>
                            <group string="Status">
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>

                        <notebook>
                            <page name="job_description" string="Job Description">
                                <group>
                                    <field name="job_description" nolabel="1"
                                           placeholder="Enter detailed job description here..."
                                           widget="html" options="{'style-inline': true}"/>
                                </group>
                            </page>

                            <page name="tasks_responsibilities" string="Tasks &amp; Responsibilities">
                                <group>
                                    <group string="Main Tasks">
                                        <field name="main_tasks" nolabel="1"
                                               placeholder="List the main tasks and duties..."
                                               widget="html" options="{'style-inline': true}"/>
                                    </group>
                                    <group string="Key Responsibilities">
                                        <field name="responsibilities" nolabel="1"
                                               placeholder="Describe key responsibilities and accountabilities..."
                                               widget="html" options="{'style-inline': true}"/>
                                    </group>
                                </group>
                            </page>

                            <page name="requirements" string="Requirements">
                                <group>
                                    <group string="Required Skills">
                                        <field name="required_skills" nolabel="1"
                                               placeholder="List technical and soft skills required..."
                                               widget="html" options="{'style-inline': true}"/>
                                    </group>
                                    <group string="Qualifications">
                                        <field name="required_qualifications" nolabel="1"
                                               placeholder="Educational qualifications and certifications..."
                                               widget="html" options="{'style-inline': true}"/>
                                    </group>
                                    <group string="Experience">
                                        <field name="required_experience" nolabel="1"
                                               placeholder="Work experience requirements..."
                                               widget="html" options="{'style-inline': true}"/>
                                    </group>
                                </group>
                            </page>

                            <page name="notes" string="Additional Notes">
                                <group>
                                    <field name="notes" nolabel="1"
                                           placeholder="Any additional information or notes..."/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Job Description Tree View -->
        <record id="view_job_description_tree" model="ir.ui.view">
            <field name="name">hr.job.description.tree</field>
            <field name="model">hr.job.description</field>
            <field name="arch" type="xml">
                <tree string="Job Descriptions" decoration-muted="state == 'inactive'">
                    <field name="name"/>
                    <field name="employee_name"/>
                    <field name="job_title"/>
                    <field name="department_name"/>
                    <field name="approval_date"/>
                    <field name="state" widget="badge"
                           decoration-success="state == 'active'"
                           decoration-muted="state == 'inactive'"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Job Description Search View -->
        <record id="view_job_description_search" model="ir.ui.view">
            <field name="name">hr.job.description.search</field>
            <field name="model">hr.job.description</field>
            <field name="arch" type="xml">
                <search string="Job Descriptions">
                    <field name="name" string="Title"/>
                    <field name="employee_id" string="Employee"/>
                    <field name="job_title" string="Job Title"/>
                    <field name="department_id" string="Department"/>
                    <separator/>
                    <filter name="active" string="Active" domain="[('state', '=', 'active')]"/>
                    <filter name="inactive" string="Inactive" domain="[('state', '=', 'inactive')]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter name="group_employee" string="Employee" context="{'group_by': 'employee_id'}"/>
                        <filter name="group_department" string="Department" context="{'group_by': 'department_id'}"/>
                        <filter name="group_state" string="Status" context="{'group_by': 'state'}"/>
                        <filter name="group_approval_date" string="Approval Date" context="{'group_by': 'approval_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Job Description Action -->
        <record id="action_job_description" model="ir.actions.act_window">
            <field name="name">Job Descriptions</field>
            <field name="res_model">hr.job.description</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_job_description_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first job description
                </p>
                <p>
                    Job descriptions help define roles, responsibilities, and requirements
                    for each position in your organization. Create comprehensive job
                    descriptions to improve clarity and performance management.
                </p>
            </field>
        </record>

    </data>
</odoo>
