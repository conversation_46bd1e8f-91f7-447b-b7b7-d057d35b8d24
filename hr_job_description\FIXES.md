# إصلاحات سريعة | Quick Fixes

## المشاكل التي تم حلها | Fixed Issues

### 1. خطأ في ملف البيانات | Data File Error

**المشكلة**: خطأ في parsing ملف `job_description_data.xml`

**الحل**: تم إصلاح الكود في base.automation باستخدام CDATA

```xml
<field name="code"><![CDATA[
for record in records:
    if record.employee_id.work_email:
        template = env.ref('hr_job_description.email_template_job_description_approval')
        template.send_mail(record.id, force_send=True)
]]></field>
```

### 2. خطأ في البيانات التجريبية | Demo Data Error

**المشكلة**: استخدام DateTime بدلاً من datetime

**الحل**: تم استخدام تواريخ ثابتة بدلاً من الديناميكية

```xml
<field name="approval_date">2024-01-01</field>
```

### 3. خطأ في Cron Job | Cron Job Error

**المشكلة**: استخدام model بدلاً من env

**الحل**: تم تصحيح الكود

```xml
<field name="code">env['hr.job.description']._cron_approval_reminder()</field>
```

## إصلاحات إضافية | Additional Fixes

### إذا واجهت مشكلة في التثبيت | Installation Issues

1. **تأكد من المتطلبات**:
   ```bash
   # تأكد من تثبيت موديول hr
   ./odoo-bin -d your_database -i hr
   ```

2. **تحديث قائمة التطبيقات**:
   ```bash
   # من سطر الأوامر
   ./odoo-bin -d your_database --update-list
   ```

3. **تثبيت الموديول**:
   ```bash
   ./odoo-bin -d your_database -i hr_job_description
   ```

### إذا واجهت مشكلة في الصلاحيات | Permission Issues

1. **تحقق من المجموعات**:
   - تأكد من أن المستخدم في مجموعة HR User أو HR Manager

2. **إعادة تحميل الصلاحيات**:
   ```python
   # في Python shell
   self.env['ir.model.access'].call_cache_clearing_methods()
   ```

### إذا لم تظهر القوائم | Menu Not Showing

1. **تحديث الموديول**:
   ```bash
   ./odoo-bin -d your_database -u hr_job_description
   ```

2. **مسح الكاش**:
   - إعدادات → تطوير → مسح الكاش

### إذا لم يعمل التقرير | Report Not Working

1. **تحقق من wkhtmltopdf**:
   ```bash
   which wkhtmltopdf
   ```

2. **تثبيت wkhtmltopdf إذا لم يكن مثبتاً**:
   ```bash
   sudo apt-get install wkhtmltopdf
   ```

## أوامر مفيدة | Useful Commands

### تحديث الموديول | Update Module
```bash
./odoo-bin -d your_database -u hr_job_description
```

### إعادة تثبيت الموديول | Reinstall Module
```bash
./odoo-bin -d your_database -i hr_job_description --without-demo=False
```

### تشغيل الاختبارات | Run Tests
```bash
./odoo-bin -d your_database --test-enable --stop-after-init -i hr_job_description
```

### عرض السجلات | View Logs
```bash
tail -f /var/log/odoo/odoo.log
```

## نصائح للتطوير | Development Tips

### تفعيل وضع المطور | Enable Developer Mode
1. إعدادات → تفعيل وضع المطور
2. أو إضافة `?debug=1` للرابط

### إعادة تحميل الملفات | Reload Files
- Ctrl+F5 في المتصفح
- أو إعدادات → تطوير → إعادة تحميل

### فحص البيانات | Inspect Data
```python
# في Python shell
records = self.env['hr.job.description'].search([])
for record in records:
    print(f"{record.name}: {record.state}")
```

## استكشاف الأخطاء | Troubleshooting

### خطأ في الاستيراد | Import Error
```
ModuleNotFoundError: No module named 'hr_job_description'
```

**الحل**:
1. تأكد من وجود الملفات في المجلد الصحيح
2. أعد تشغيل Odoo
3. تحقق من ملف `__init__.py`

### خطأ في قاعدة البيانات | Database Error
```
relation "hr_job_description" does not exist
```

**الحل**:
```bash
./odoo-bin -d your_database -u base
./odoo-bin -d your_database -i hr_job_description
```

### خطأ في الصلاحيات | Access Error
```
You do not have access to this document
```

**الحل**:
1. تحقق من ملف `ir.model.access.csv`
2. تأكد من الصلاحيات الصحيحة للمستخدم

## الدعم | Support

إذا واجهت أي مشكلة أخرى:

1. **تحقق من السجلات**: `tail -f /var/log/odoo/odoo.log`
2. **فعل وضع المطور**: للحصول على تفاصيل أكثر
3. **تحقق من الإصدار**: تأكد من استخدام Odoo 15
4. **راجع الوثائق**: اقرأ ملف README.md

---

**تم إصلاح جميع المشاكل المعروفة. الموديول جاهز للاستخدام! ✅**
