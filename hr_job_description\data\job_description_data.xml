<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Job Description Sequence -->
        <record id="sequence_job_description" model="ir.sequence">
            <field name="name">Job Description Sequence</field>
            <field name="code">hr.job.description</field>
            <field name="prefix">JD</field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="company_id" eval="False"/>
        </record>

        <!-- Email Template for Job Description Approval -->
        <record id="email_template_job_description_approval" model="mail.template">
            <field name="name">Job Description Approval Notification</field>
            <field name="model_id" ref="model_hr_job_description"/>
            <field name="subject">Job Description Approved: ${object.name}</field>
            <field name="email_from">${(object.company_id.email or user.email)|safe}</field>
            <field name="email_to">${object.employee_id.work_email}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        Dear ${object.employee_id.name},
                        <br/><br/>
                        Your job description has been approved and is now active.
                        <br/><br/>
                        <strong>Job Title:</strong> ${object.job_title}<br/>
                        <strong>Department:</strong> ${object.department_id.name}<br/>
                        <strong>Approval Date:</strong> ${object.approval_date}<br/>
                        <br/>
                        You can view the complete job description by logging into the system.
                        <br/><br/>
                        Best regards,<br/>
                        ${object.company_id.name} HR Team
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <!-- Automated Action: Send email when job description is activated -->
        <record id="action_job_description_activated" model="base.automation">
            <field name="name">Job Description Activated</field>
            <field name="model_id" ref="model_hr_job_description"/>
            <field name="state">code</field>
            <field name="trigger">on_write</field>
            <field name="filter_domain">[('state', '=', 'active')]</field>
            <field name="code"><![CDATA[
for record in records:
    if record.employee_id.work_email:
        template = env.ref('hr_job_description.email_template_job_description_approval')
        template.send_mail(record.id, force_send=True)
            ]]></field>
        </record>

        <!-- Cron Job: Remind about job descriptions without approval date -->
        <record id="cron_job_description_reminder" model="ir.cron">
            <field name="name">Job Description Approval Reminder</field>
            <field name="model_id" ref="model_hr_job_description"/>
            <field name="state">code</field>
            <field name="code">env['hr.job.description']._cron_approval_reminder()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">weeks</field>
            <field name="numbercall">-1</field>
            <field name="active" eval="False"/>
        </record>

    </data>
</odoo>
