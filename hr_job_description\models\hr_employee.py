# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # Job Description Fields
    job_description_ids = fields.One2many(
        'hr.job.description',
        'employee_id',
        string='Job Descriptions',
        help='All job descriptions for this employee'
    )
    
    active_job_description_id = fields.Many2one(
        'hr.job.description',
        string='Active Job Description',
        compute='_compute_active_job_description',
        store=True,
        help='Currently active job description for this employee'
    )
    
    job_description_count = fields.Integer(
        string='Job Descriptions Count',
        compute='_compute_job_description_count',
        help='Total number of job descriptions for this employee'
    )
    
    has_job_description = fields.Boolean(
        string='Has Job Description',
        compute='_compute_has_job_description',
        store=True,
        help='Whether this employee has any job description'
    )

    @api.depends('job_description_ids', 'job_description_ids.state')
    def _compute_active_job_description(self):
        """Compute the active job description for each employee"""
        for employee in self:
            active_desc = employee.job_description_ids.filtered(
                lambda x: x.state == 'active'
            )
            employee.active_job_description_id = active_desc[0] if active_desc else False

    @api.depends('job_description_ids')
    def _compute_job_description_count(self):
        """Compute the total count of job descriptions"""
        for employee in self:
            employee.job_description_count = len(employee.job_description_ids)

    @api.depends('job_description_ids')
    def _compute_has_job_description(self):
        """Compute whether employee has any job description"""
        for employee in self:
            employee.has_job_description = bool(employee.job_description_ids)

    def action_view_job_descriptions(self):
        """Action to view all job descriptions for this employee"""
        self.ensure_one()
        action = self.env.ref('hr_job_description.action_job_description').read()[0]
        action['domain'] = [('employee_id', '=', self.id)]
        action['context'] = {
            'default_employee_id': self.id,
            'default_job_title': self.job_title or (self.job_id.name if self.job_id else ''),
            'default_department_id': self.department_id.id if self.department_id else False,
        }
        if len(self.job_description_ids) == 1:
            action['views'] = [(False, 'form')]
            action['res_id'] = self.job_description_ids.id
        return action

    def action_create_job_description(self):
        """Action to create a new job description for this employee"""
        self.ensure_one()
        action = self.env.ref('hr_job_description.action_job_description').read()[0]
        action['views'] = [(False, 'form')]
        action['context'] = {
            'default_employee_id': self.id,
            'default_job_title': self.job_title or (self.job_id.name if self.job_id else ''),
            'default_department_id': self.department_id.id if self.department_id else False,
            'default_name': _('Job Description - %s') % self.name,
        }
        return action

    def action_view_active_job_description(self):
        """Action to view the active job description"""
        self.ensure_one()
        if not self.active_job_description_id:
            return self.action_create_job_description()
        
        action = self.env.ref('hr_job_description.action_job_description').read()[0]
        action['views'] = [(False, 'form')]
        action['res_id'] = self.active_job_description_id.id
        return action
