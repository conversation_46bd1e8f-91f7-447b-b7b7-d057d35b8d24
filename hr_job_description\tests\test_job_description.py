# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError


class TestJobDescription(TransactionCase):

    def setUp(self):
        super(TestJobDescription, self).setUp()
        
        # Create test employee
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'job_title': 'Test Developer',
        })
        
        # Create test department
        self.department = self.env['hr.department'].create({
            'name': 'Test Department',
        })

    def test_job_description_creation(self):
        """Test creating a job description"""
        job_desc = self.env['hr.job.description'].create({
            'name': 'Test Job Description',
            'employee_id': self.employee.id,
            'job_title': 'Senior Developer',
            'department_id': self.department.id,
            'job_description': '<p>Test description</p>',
            'state': 'active',
        })
        
        self.assertEqual(job_desc.employee_name, 'Test Employee')
        self.assertEqual(job_desc.department_name, 'Test Department')
        self.assertEqual(job_desc.state, 'active')

    def test_employee_job_description_relation(self):
        """Test employee-job description relationship"""
        job_desc = self.env['hr.job.description'].create({
            'name': 'Test Job Description',
            'employee_id': self.employee.id,
            'job_title': 'Senior Developer',
            'department_id': self.department.id,
            'state': 'active',
        })
        
        # Check employee computed fields
        self.assertEqual(self.employee.job_description_count, 1)
        self.assertTrue(self.employee.has_job_description)
        self.assertEqual(self.employee.active_job_description_id, job_desc)

    def test_only_one_active_job_description(self):
        """Test that only one job description can be active per employee"""
        # Create first active job description
        job_desc1 = self.env['hr.job.description'].create({
            'name': 'Test Job Description 1',
            'employee_id': self.employee.id,
            'job_title': 'Senior Developer',
            'department_id': self.department.id,
            'state': 'active',
        })
        
        # Try to create second active job description - should raise error
        with self.assertRaises(ValidationError):
            self.env['hr.job.description'].create({
                'name': 'Test Job Description 2',
                'employee_id': self.employee.id,
                'job_title': 'Lead Developer',
                'department_id': self.department.id,
                'state': 'active',
            })

    def test_activate_deactivate_actions(self):
        """Test activate/deactivate actions"""
        # Create two job descriptions
        job_desc1 = self.env['hr.job.description'].create({
            'name': 'Test Job Description 1',
            'employee_id': self.employee.id,
            'job_title': 'Senior Developer',
            'department_id': self.department.id,
            'state': 'active',
        })
        
        job_desc2 = self.env['hr.job.description'].create({
            'name': 'Test Job Description 2',
            'employee_id': self.employee.id,
            'job_title': 'Lead Developer',
            'department_id': self.department.id,
            'state': 'inactive',
        })
        
        # Activate second job description
        job_desc2.action_activate()
        
        # Check that first is deactivated and second is activated
        self.assertEqual(job_desc1.state, 'inactive')
        self.assertEqual(job_desc2.state, 'active')
        self.assertEqual(self.employee.active_job_description_id, job_desc2)

    def test_onchange_employee(self):
        """Test onchange employee method"""
        # Update employee with job info
        self.employee.write({
            'job_title': 'Senior Developer',
            'department_id': self.department.id,
        })
        
        # Create job description and trigger onchange
        job_desc = self.env['hr.job.description'].new({
            'employee_id': self.employee.id,
        })
        job_desc._onchange_employee_id()
        
        # Check that fields are auto-filled
        self.assertEqual(job_desc.job_title, 'Senior Developer')
        self.assertEqual(job_desc.department_id, self.department)
        self.assertEqual(job_desc.name, 'Job Description - Test Employee')

    def test_name_get(self):
        """Test custom name_get method"""
        job_desc = self.env['hr.job.description'].create({
            'name': 'Test Job Description',
            'employee_id': self.employee.id,
            'job_title': 'Senior Developer',
            'department_id': self.department.id,
            'state': 'active',
        })
        
        name = job_desc.name_get()[0][1]
        self.assertEqual(name, 'Test Employee - Senior Developer')
        
        # Test inactive job description
        job_desc.write({'state': 'inactive'})
        name = job_desc.name_get()[0][1]
        self.assertIn('(Inactive)', name)

    def test_employee_actions(self):
        """Test employee action methods"""
        # Test action_create_job_description
        action = self.employee.action_create_job_description()
        self.assertEqual(action['res_model'], 'hr.job.description')
        self.assertEqual(action['context']['default_employee_id'], self.employee.id)
        
        # Create a job description
        job_desc = self.env['hr.job.description'].create({
            'name': 'Test Job Description',
            'employee_id': self.employee.id,
            'job_title': 'Senior Developer',
            'department_id': self.department.id,
            'state': 'active',
        })
        
        # Test action_view_job_descriptions
        action = self.employee.action_view_job_descriptions()
        self.assertEqual(action['domain'], [('employee_id', '=', self.employee.id)])
        
        # Test action_view_active_job_description
        action = self.employee.action_view_active_job_description()
        self.assertEqual(action['res_id'], job_desc.id)
