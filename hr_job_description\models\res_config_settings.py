# -*- coding: utf-8 -*-

from odoo import fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    job_description_auto_approval = fields.Boolean(
        string='Auto-approve Job Descriptions',
        config_parameter='hr_job_description.auto_approval',
        help='Automatically approve job descriptions when created'
    )
    
    job_description_reminder_enabled = fields.Boolean(
        string='Enable Approval Reminders',
        config_parameter='hr_job_description.reminder_enabled',
        help='Send reminders for job descriptions pending approval'
    )
    
    job_description_reminder_days = fields.Integer(
        string='Reminder Days',
        config_parameter='hr_job_description.reminder_days',
        default=30,
        help='Number of days after which to send approval reminders'
    )
