<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>HR Job Description Module</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #2E86AB;
        }
        .feature h3 {
            color: #2E86AB;
            margin-top: 0;
        }
        .feature-icon {
            font-size: 2em;
            color: #2E86AB;
            margin-bottom: 15px;
        }
        .screenshot {
            text-align: center;
            margin: 40px 0;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .benefits {
            background: #e3f2fd;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
        }
        .benefits h2 {
            color: #1976d2;
            text-align: center;
        }
        .benefits ul {
            list-style: none;
            padding: 0;
        }
        .benefits li {
            padding: 10px 0;
            border-bottom: 1px solid #ddd;
        }
        .benefits li:before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>HR Job Description</h1>
            <p>Professional Job Description Management for Odoo 15</p>
        </div>
        
        <div class="content">
            <h2>Overview</h2>
            <p>
                The HR Job Description module provides a comprehensive solution for managing detailed job descriptions 
                within your Odoo HR system. Create, manage, and maintain professional job descriptions with rich content 
                editing capabilities, professional PDF reports, and seamless integration with employee profiles.
            </p>

            <div class="features">
                <div class="feature">
                    <div class="feature-icon">📋</div>
                    <h3>Rich Job Descriptions</h3>
                    <p>Create detailed job descriptions with rich text editing, including main tasks, responsibilities, required skills, qualifications, and experience requirements.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">👤</div>
                    <h3>Employee Integration</h3>
                    <p>Seamlessly integrated with employee profiles through a dedicated tab, smart buttons, and automatic field population from employee data.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <h3>Professional Reports</h3>
                    <p>Generate beautiful PDF reports with company branding, organized sections, and professional formatting for official documentation.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">🌐</div>
                    <h3>Bilingual Support</h3>
                    <p>Full Arabic and English language support with complete translations for all interface elements and reports.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">🔒</div>
                    <h3>Access Control</h3>
                    <p>Proper security implementation with role-based access control for HR users, managers, and employees.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3>Status Management</h3>
                    <p>Track job description status (Active/Inactive) with automatic validation to ensure only one active description per employee.</p>
                </div>
            </div>

            <div class="benefits">
                <h2>Key Benefits</h2>
                <ul>
                    <li>Streamlined job description creation and management process</li>
                    <li>Professional PDF reports for official documentation</li>
                    <li>Rich text editing for detailed and formatted content</li>
                    <li>Automatic integration with existing employee data</li>
                    <li>Bilingual interface supporting Arabic and English</li>
                    <li>Smart buttons and counters for quick access</li>
                    <li>Organized menu structure within HR module</li>
                    <li>Status tracking and validation</li>
                    <li>Activity tracking and messaging integration</li>
                    <li>Responsive design for all screen sizes</li>
                </ul>
            </div>

            <h2>Features Included</h2>
            <div class="features">
                <div class="feature">
                    <h3>Job Description Form</h3>
                    <p>Comprehensive form with sections for basic information, detailed description, tasks, responsibilities, and requirements.</p>
                </div>

                <div class="feature">
                    <h3>Employee Tab</h3>
                    <p>Dedicated "Job Description" tab in employee form with smart buttons, active description display, and quick actions.</p>
                </div>

                <div class="feature">
                    <h3>Menu Integration</h3>
                    <p>Organized menu structure under HR with separate views for active and all job descriptions.</p>
                </div>

                <div class="feature">
                    <h3>PDF Reports</h3>
                    <p>Professional PDF reports with company branding, organized sections, and beautiful formatting.</p>
                </div>
            </div>

            <h2>Installation & Usage</h2>
            <p>
                <strong>Installation:</strong> Simply install the module through the Odoo Apps menu. 
                The module depends on the base HR module which should be installed first.
            </p>
            <p>
                <strong>Usage:</strong> Navigate to HR → Job Descriptions to start creating job descriptions, 
                or access them directly from employee profiles through the new "Job Description" tab.
            </p>

            <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f5f5f5; border-radius: 10px;">
                <h3>Ready to Get Started?</h3>
                <p>Install the HR Job Description module today and streamline your job description management process!</p>
            </div>
        </div>
    </div>
</body>
</html>
