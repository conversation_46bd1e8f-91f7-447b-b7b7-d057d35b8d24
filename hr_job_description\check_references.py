#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reference Checker for HR Job Description Module
This script checks all XML references are correct
"""

import os
import re
import xml.etree.ElementTree as ET
from pathlib import Path

def check_references():
    """Check all XML references in the module"""
    
    print("🔍 Checking XML References...")
    print("=" * 40)
    
    errors = []
    warnings = []
    
    # Define all IDs that should exist
    defined_ids = set()
    referenced_ids = set()
    
    # XML files to check
    xml_files = [
        'views/job_description_views.xml',
        'views/hr_employee_views.xml',
        'views/job_description_menu.xml',
        'views/res_config_settings_views.xml',
        'report/job_description_report.xml',
        'report/job_description_template.xml',
        'data/job_description_data.xml',
        'demo/job_description_demo.xml',
    ]
    
    # First pass: collect all defined IDs
    print("📋 Collecting defined IDs...")
    for xml_file in xml_files:
        if os.path.exists(xml_file):
            try:
                tree = ET.parse(xml_file)
                root = tree.getroot()
                
                # Find all record IDs
                for record in root.findall('.//record'):
                    record_id = record.get('id')
                    if record_id:
                        defined_ids.add(record_id)
                        print(f"  ✅ Found ID: {record_id} in {xml_file}")
                
                # Find all template IDs
                for template in root.findall('.//template'):
                    template_id = template.get('id')
                    if template_id:
                        defined_ids.add(template_id)
                        print(f"  ✅ Found template ID: {template_id} in {xml_file}")
                        
            except ET.ParseError as e:
                errors.append(f"XML parse error in {xml_file}: {e}")
    
    print(f"\n📊 Total defined IDs: {len(defined_ids)}")
    
    # Second pass: collect all referenced IDs
    print("\n🔗 Checking references...")
    for xml_file in xml_files:
        if os.path.exists(xml_file):
            try:
                with open(xml_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find all ref= references
                ref_pattern = r'ref="([^"]+)"'
                refs = re.findall(ref_pattern, content)
                
                for ref in refs:
                    referenced_ids.add(ref)
                    # Check if it's a local reference (not external like hr.something)
                    if '.' not in ref or ref.startswith('hr_job_description.'):
                        local_ref = ref.replace('hr_job_description.', '')
                        if local_ref not in defined_ids:
                            errors.append(f"Undefined reference '{ref}' in {xml_file}")
                            print(f"  ❌ Undefined: {ref} in {xml_file}")
                        else:
                            print(f"  ✅ Valid: {ref} in {xml_file}")
                    else:
                        print(f"  ℹ️  External: {ref} in {xml_file}")
                
                # Find all %(...)d references
                action_pattern = r'%\(([^)]+)\)d'
                actions = re.findall(action_pattern, content)
                
                for action in actions:
                    referenced_ids.add(action)
                    if action not in defined_ids:
                        errors.append(f"Undefined action reference '{action}' in {xml_file}")
                        print(f"  ❌ Undefined action: {action} in {xml_file}")
                    else:
                        print(f"  ✅ Valid action: {action} in {xml_file}")
                        
            except Exception as e:
                errors.append(f"Error reading {xml_file}: {e}")
    
    print(f"\n📊 Total referenced IDs: {len(referenced_ids)}")
    
    # Check for unused IDs
    print("\n🔍 Checking for unused IDs...")
    unused_ids = defined_ids - referenced_ids
    for unused_id in unused_ids:
        # Skip some IDs that are expected to be unused
        if unused_id not in ['sequence_job_description', 'email_template_job_description_approval']:
            warnings.append(f"Unused ID: {unused_id}")
            print(f"  ⚠️  Unused: {unused_id}")
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 REFERENCE CHECK SUMMARY")
    print("=" * 40)
    
    if not errors:
        print("🎉 SUCCESS: All references are valid!")
        print(f"✅ {len(defined_ids)} IDs defined")
        print(f"✅ {len(referenced_ids)} references checked")
        
        if warnings:
            print(f"\n⚠️  {len(warnings)} warnings:")
            for warning in warnings:
                print(f"   • {warning}")
    else:
        print(f"❌ FAILED: {len(errors)} reference errors found:")
        for error in errors:
            print(f"   • {error}")
        
        if warnings:
            print(f"\n⚠️  {len(warnings)} warnings:")
            for warning in warnings:
                print(f"   • {warning}")
    
    print("\n" + "=" * 40)
    
    return len(errors) == 0

if __name__ == "__main__":
    # Change to module directory
    module_dir = Path(__file__).parent
    os.chdir(module_dir)
    
    success = check_references()
    exit(0 if success else 1)
