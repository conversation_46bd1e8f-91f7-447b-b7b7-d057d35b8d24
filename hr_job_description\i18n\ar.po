# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_job_description
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: 2024-01-01 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: hr_job_description
#: model:ir.actions.act_window,name:hr_job_description.action_job_description_active
#: model:ir.ui.menu,name:hr_job_description.menu_job_description_active
msgid "Active Job Descriptions"
msgstr "أوصاف الوظائف النشطة"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__state
#: model:ir.model.fields,field_description:hr_job_description.field_hr_employee__active_job_description_id
msgid "Active Job Description"
msgstr "وصف الوظيفة النشط"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_employee__active_job_description_id
msgid "Currently active job description for this employee"
msgstr "وصف الوظيفة النشط حالياً لهذا الموظف"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__notes
msgid "Additional Notes"
msgstr "ملاحظات إضافية"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__notes
msgid "Any additional information or notes"
msgstr "أي معلومات أو ملاحظات إضافية"

#. module: hr_job_description
#: model:ir.actions.act_window,name:hr_job_description.action_job_description_all
#: model:ir.ui.menu,name:hr_job_description.menu_job_description_all
msgid "All Job Descriptions"
msgstr "جميع أوصاف الوظائف"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__approval_date
msgid "Approval Date"
msgstr "تاريخ الاعتماد"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__approval_date
msgid "Date when this job description was approved"
msgstr "التاريخ الذي تم فيه اعتماد وصف الوظيفة هذا"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__company_id
msgid "Company"
msgstr "الشركة"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__create_date
msgid "Created on"
msgstr "تاريخ الإنشاء"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__department_id
msgid "Department"
msgstr "القسم"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__department_name
msgid "Department Name"
msgstr "اسم القسم"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__department_id
msgid "Department where this position belongs"
msgstr "القسم الذي تنتمي إليه هذه الوظيفة"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__employee_id
msgid "Employee"
msgstr "الموظف"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__employee_name
msgid "Employee Name"
msgstr "اسم الموظف"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__employee_id
msgid "Employee for whom this job description is created"
msgstr "الموظف الذي تم إنشاء وصف الوظيفة له"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_employee__has_job_description
msgid "Has Job Description"
msgstr "لديه وصف وظيفة"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_employee__has_job_description
msgid "Whether this employee has any job description"
msgstr "ما إذا كان لدى هذا الموظف أي وصف وظيفة"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__job_description
msgid "Job Description"
msgstr "وصف الوظيفة"

#. module: hr_job_description
#: model:ir.actions.act_window,name:hr_job_description.action_job_description
#: model:ir.actions.report,name:hr_job_description.action_job_description_report
#: model:ir.ui.menu,name:hr_job_description.menu_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_employee__job_description_ids
msgid "Job Descriptions"
msgstr "أوصاف الوظائف"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_employee__job_description_count
msgid "Job Descriptions Count"
msgstr "عدد أوصاف الوظائف"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__name
msgid "Job Description Title"
msgstr "عنوان وصف الوظيفة"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__job_title
msgid "Job Title"
msgstr "المسمى الوظيفي"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__job_title
msgid "Official job title for this position"
msgstr "المسمى الوظيفي الرسمي لهذا المنصب"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__main_tasks
msgid "Main Tasks"
msgstr "المهام الرئيسية"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__main_tasks
msgid "Primary tasks and duties of this position"
msgstr "المهام والواجبات الأساسية لهذا المنصب"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__required_experience
msgid "Required Experience"
msgstr "الخبرة المطلوبة"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__required_experience
msgid "Work experience requirements for this position"
msgstr "متطلبات الخبرة العملية لهذا المنصب"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__required_qualifications
msgid "Required Qualifications"
msgstr "المؤهلات المطلوبة"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__required_qualifications
msgid "Educational qualifications and certifications required"
msgstr "المؤهلات التعليمية والشهادات المطلوبة"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__required_skills
msgid "Required Skills"
msgstr "المهارات المطلوبة"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__required_skills
msgid "Technical and soft skills required for this position"
msgstr "المهارات التقنية والشخصية المطلوبة لهذا المنصب"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__responsibilities
msgid "Responsibilities"
msgstr "المسؤوليات"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__responsibilities
msgid "Key responsibilities and accountabilities"
msgstr "المسؤوليات والمساءلات الرئيسية"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_hr_job_description__state
#: model:ir.model.fields,selection:hr_job_description.field_hr_job_description__state
msgid "Status"
msgstr "الحالة"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_job_description__state
msgid "Current status of this job description"
msgstr "الحالة الحالية لوصف الوظيفة هذا"

#. module: hr_job_description
#: model:ir.model.fields,help:hr_job_description.field_hr_employee__job_description_count
msgid "Total number of job descriptions for this employee"
msgstr "العدد الإجمالي لأوصاف الوظائف لهذا الموظف"

#. module: hr_job_description
#: model:ir.model.fields,selection:hr_job_description.field_hr_job_description__state
msgid "Active"
msgstr "نشط"

#. module: hr_job_description
#: model:ir.model.fields,selection:hr_job_description.field_hr_job_description__state
msgid "Inactive"
msgstr "غير نشط"

#. module: hr_job_description
#: code:addons/hr_job_description/models/job_description.py:0
#, python-format
msgid "Job Description - %s"
msgstr "وصف الوظيفة - %s"

#. module: hr_job_description
#: code:addons/hr_job_description/models/hr_employee.py:0
#, python-format
msgid "Job Description - %s"
msgstr "وصف الوظيفة - %s"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_res_config_settings__job_description_auto_approval
msgid "Auto-approve Job Descriptions"
msgstr "الموافقة التلقائية على أوصاف الوظائف"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_res_config_settings__job_description_reminder_enabled
msgid "Enable Approval Reminders"
msgstr "تفعيل تذكيرات الموافقة"

#. module: hr_job_description
#: model:ir.model.fields,field_description:hr_job_description.field_res_config_settings__job_description_reminder_days
msgid "Reminder Days"
msgstr "أيام التذكير"

#. module: hr_job_description
#: code:addons/hr_job_description/models/job_description.py:0
#, python-format
msgid "Job Description Approval Required"
msgstr "مطلوب الموافقة على وصف الوظيفة"

#. module: hr_job_description
#: code:addons/hr_job_description/models/job_description.py:0
#, python-format
msgid "Please review and approve the job description for %s"
msgstr "يرجى مراجعة والموافقة على وصف الوظيفة لـ %s"

#. module: hr_job_description
msgid "Create Job Description"
msgstr "إنشاء وصف وظيفة"

#. module: hr_job_description
msgid "View All"
msgstr "عرض الكل"

#. module: hr_job_description
msgid "Create New"
msgstr "إنشاء جديد"

#. module: hr_job_description
msgid "Print Active"
msgstr "طباعة النشط"

#. module: hr_job_description
msgid "Print PDF"
msgstr "طباعة PDF"

#. module: hr_job_description
msgid "Activate"
msgstr "تفعيل"

#. module: hr_job_description
msgid "Deactivate"
msgstr "إلغاء التفعيل"

#. module: hr_job_description
msgid "Job Description Management"
msgstr "إدارة أوصاف الوظائف"

#. module: hr_job_description
msgid "No Job Description Found"
msgstr "لم يتم العثور على وصف وظيفة"

#. module: hr_job_description
msgid "Create First Job Description"
msgstr "إنشاء أول وصف وظيفة"

#. module: hr_job_description
msgid "All Job Descriptions"
msgstr "جميع أوصاف الوظائف"

#. module: hr_job_description
msgid "Quick Actions"
msgstr "إجراءات سريعة"
