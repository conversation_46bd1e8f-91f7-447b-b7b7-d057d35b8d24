# -*- coding: utf-8 -*-

def migrate(cr, version):
    """Post-migration script for hr_job_description module"""
    
    # Update existing employees to compute job description fields
    cr.execute("""
        UPDATE hr_employee 
        SET job_description_count = (
            SELECT COUNT(*) 
            FROM hr_job_description 
            WHERE employee_id = hr_employee.id
        ),
        has_job_description = (
            SELECT COUNT(*) > 0 
            FROM hr_job_description 
            WHERE employee_id = hr_employee.id
        )
    """)
    
    # Set active job description for employees
    cr.execute("""
        UPDATE hr_employee 
        SET active_job_description_id = (
            SELECT id 
            FROM hr_job_description 
            WHERE employee_id = hr_employee.id 
            AND state = 'active' 
            LIMIT 1
        )
    """)
    
    print("HR Job Description module migration completed successfully.")
