#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module Validation Script for HR Job Description
This script validates the module structure and files
"""

import os
import sys
import xml.etree.ElementTree as ET
from pathlib import Path

def validate_module():
    """Validate the HR Job Description module"""

    print("🔍 Validating HR Job Description Module...")
    print("=" * 50)

    errors = []
    warnings = []

    # Check required files
    required_files = [
        '__init__.py',
        '__manifest__.py',
        'models/__init__.py',
        'models/job_description.py',
        'models/hr_employee.py',
        'views/job_description_views.xml',
        'views/hr_employee_views.xml',
        'security/ir.model.access.csv',
    ]

    print("📁 Checking required files...")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            errors.append(f"Missing required file: {file_path}")
            print(f"  ❌ {file_path}")

    # Check XML files syntax
    print("\n🔧 Checking XML syntax...")
    xml_files = [
        'views/job_description_views.xml',
        'views/hr_employee_views.xml',
        'views/job_description_menu.xml',
        'report/job_description_report.xml',
        'report/job_description_template.xml',
        'data/job_description_data.xml',
        'demo/job_description_demo.xml',
    ]

    for xml_file in xml_files:
        if os.path.exists(xml_file):
            try:
                ET.parse(xml_file)
                print(f"  ✅ {xml_file}")
            except ET.ParseError as e:
                errors.append(f"XML syntax error in {xml_file}: {e}")
                print(f"  ❌ {xml_file}: {e}")
        else:
            warnings.append(f"Optional XML file not found: {xml_file}")

    # Check Python files syntax
    print("\n🐍 Checking Python syntax...")
    python_files = [
        'models/job_description.py',
        'models/hr_employee.py',
        'models/res_config_settings.py',
        'tests/test_job_description.py',
    ]

    for py_file in python_files:
        if os.path.exists(py_file):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    compile(f.read(), py_file, 'exec')
                print(f"  ✅ {py_file}")
            except SyntaxError as e:
                errors.append(f"Python syntax error in {py_file}: {e}")
                print(f"  ❌ {py_file}: {e}")
        else:
            if py_file in ['models/job_description.py', 'models/hr_employee.py']:
                errors.append(f"Missing required Python file: {py_file}")
            else:
                warnings.append(f"Optional Python file not found: {py_file}")

    # Check manifest file
    print("\n📋 Checking manifest file...")
    if os.path.exists('__manifest__.py'):
        try:
            import ast
            with open('__manifest__.py', 'r', encoding='utf-8') as f:
                manifest_content = f.read()
                # Parse the file as AST to safely evaluate
                tree = ast.parse(manifest_content)
                # Find the dictionary assignment
                for node in ast.walk(tree):
                    if isinstance(node, ast.Dict):
                        manifest = ast.literal_eval(node)
                        break
                else:
                    # Fallback: try to evaluate the whole content
                    manifest = ast.literal_eval(manifest_content.strip())

                required_keys = ['name', 'version', 'depends', 'data']
                for key in required_keys:
                    if key in manifest:
                        print(f"  ✅ {key}: {manifest[key]}")
                    else:
                        errors.append(f"Missing required key in manifest: {key}")
                        print(f"  ❌ Missing: {key}")

        except Exception as e:
            # If parsing fails, just check if file exists and is readable
            print(f"  ⚠️  Manifest syntax check skipped: {e}")
            print(f"  ✅ __manifest__.py exists and is readable")

    # Check directory structure
    print("\n📂 Checking directory structure...")
    required_dirs = ['models', 'views', 'security']
    optional_dirs = ['data', 'demo', 'report', 'static', 'tests', 'i18n']

    for dir_name in required_dirs:
        if os.path.isdir(dir_name):
            print(f"  ✅ {dir_name}/")
        else:
            errors.append(f"Missing required directory: {dir_name}")
            print(f"  ❌ {dir_name}/")

    for dir_name in optional_dirs:
        if os.path.isdir(dir_name):
            print(f"  ✅ {dir_name}/ (optional)")
        else:
            print(f"  ⚠️  {dir_name}/ (optional, not found)")

    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)

    if not errors:
        print("🎉 SUCCESS: Module validation passed!")
        print("✅ All required files and structure are correct")

        if warnings:
            print(f"\n⚠️  {len(warnings)} warnings:")
            for warning in warnings:
                print(f"   • {warning}")
    else:
        print(f"❌ FAILED: {len(errors)} errors found:")
        for error in errors:
            print(f"   • {error}")

        if warnings:
            print(f"\n⚠️  {len(warnings)} warnings:")
            for warning in warnings:
                print(f"   • {warning}")

    print("\n" + "=" * 50)

    return len(errors) == 0

if __name__ == "__main__":
    # Change to module directory
    module_dir = Path(__file__).parent
    os.chdir(module_dir)

    success = validate_module()
    sys.exit(0 if success else 1)
