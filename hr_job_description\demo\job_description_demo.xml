<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Demo Job Description 1: Software Developer -->
        <record id="job_description_demo_1" model="hr.job.description">
            <field name="name">Job Description - Software Developer</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="job_title">Senior Software Developer</field>
            <field name="department_id" ref="hr.dep_rd"/>
            <field name="state">active</field>
            <field name="approval_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="job_description"><![CDATA[
                <p>We are seeking a highly skilled Senior Software Developer to join our dynamic development team. 
                The successful candidate will be responsible for designing, developing, and maintaining high-quality 
                software applications using modern technologies and best practices.</p>
                
                <p>This role requires strong technical expertise, excellent problem-solving skills, and the ability 
                to work collaboratively in an agile development environment.</p>
            ]]></field>
            <field name="main_tasks"><![CDATA[
                <ul>
                    <li>Design and develop scalable web applications using Python, JavaScript, and modern frameworks</li>
                    <li>Write clean, maintainable, and well-documented code</li>
                    <li>Participate in code reviews and maintain coding standards</li>
                    <li>Collaborate with cross-functional teams to deliver high-quality products</li>
                    <li>Troubleshoot and debug applications to optimize performance</li>
                    <li>Stay updated with latest technology trends and best practices</li>
                </ul>
            ]]></field>
            <field name="responsibilities"><![CDATA[
                <ul>
                    <li>Lead technical discussions and provide architectural guidance</li>
                    <li>Mentor junior developers and conduct knowledge sharing sessions</li>
                    <li>Ensure application security and data protection measures</li>
                    <li>Participate in project planning and estimation activities</li>
                    <li>Maintain and improve existing codebase</li>
                    <li>Document technical specifications and user guides</li>
                </ul>
            ]]></field>
            <field name="required_skills"><![CDATA[
                <ul>
                    <li>Proficiency in Python, JavaScript, HTML, CSS</li>
                    <li>Experience with web frameworks (Django, Flask, React, Vue.js)</li>
                    <li>Knowledge of database systems (PostgreSQL, MySQL)</li>
                    <li>Familiarity with version control systems (Git)</li>
                    <li>Understanding of RESTful APIs and web services</li>
                    <li>Strong problem-solving and analytical skills</li>
                    <li>Excellent communication and teamwork abilities</li>
                </ul>
            ]]></field>
            <field name="required_qualifications"><![CDATA[
                <ul>
                    <li>Bachelor's degree in Computer Science, Software Engineering, or related field</li>
                    <li>Relevant certifications in programming languages or frameworks (preferred)</li>
                    <li>Continuous learning mindset and willingness to adapt to new technologies</li>
                </ul>
            ]]></field>
            <field name="required_experience"><![CDATA[
                <ul>
                    <li>Minimum 5 years of professional software development experience</li>
                    <li>Experience with agile development methodologies</li>
                    <li>Previous experience in leading small development teams (preferred)</li>
                    <li>Experience with cloud platforms (AWS, Azure, GCP) is a plus</li>
                </ul>
            ]]></field>
            <field name="notes">This position offers excellent growth opportunities and the chance to work on cutting-edge projects with a talented team.</field>
        </record>

        <!-- Demo Job Description 2: HR Manager -->
        <record id="job_description_demo_2" model="hr.job.description">
            <field name="name">Job Description - HR Manager</field>
            <field name="employee_id" ref="hr.employee_hne"/>
            <field name="job_title">Human Resources Manager</field>
            <field name="department_id" ref="hr.dep_administration"/>
            <field name="state">active</field>
            <field name="approval_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="job_description"><![CDATA[
                <p>We are looking for an experienced Human Resources Manager to oversee all aspects of human resources 
                practices and processes. The HR Manager will support business needs and ensure the proper implementation 
                of company strategy and objectives.</p>
                
                <p>The ideal candidate will have a strong background in HR management, excellent interpersonal skills, 
                and the ability to handle sensitive and confidential information with discretion.</p>
            ]]></field>
            <field name="main_tasks"><![CDATA[
                <ul>
                    <li>Develop and implement HR strategies and initiatives aligned with business strategy</li>
                    <li>Manage the recruitment and selection process</li>
                    <li>Oversee employee onboarding and orientation programs</li>
                    <li>Handle employee relations and resolve workplace conflicts</li>
                    <li>Manage performance evaluation systems and processes</li>
                    <li>Ensure legal compliance with employment laws and regulations</li>
                </ul>
            ]]></field>
            <field name="responsibilities"><![CDATA[
                <ul>
                    <li>Lead and manage the HR team effectively</li>
                    <li>Develop and maintain HR policies and procedures</li>
                    <li>Oversee compensation and benefits administration</li>
                    <li>Manage employee training and development programs</li>
                    <li>Handle disciplinary actions and terminations when necessary</li>
                    <li>Maintain employee records and HR databases</li>
                    <li>Prepare HR reports and analytics for management</li>
                </ul>
            ]]></field>
            <field name="required_skills"><![CDATA[
                <ul>
                    <li>Strong knowledge of HR practices and employment law</li>
                    <li>Excellent communication and interpersonal skills</li>
                    <li>Leadership and team management abilities</li>
                    <li>Problem-solving and conflict resolution skills</li>
                    <li>Proficiency in HR software and MS Office Suite</li>
                    <li>Strong organizational and time management skills</li>
                    <li>Ability to handle confidential information with discretion</li>
                </ul>
            ]]></field>
            <field name="required_qualifications"><![CDATA[
                <ul>
                    <li>Bachelor's degree in Human Resources, Business Administration, or related field</li>
                    <li>HR certification (SHRM, PHR) preferred</li>
                    <li>Master's degree in HR or related field is a plus</li>
                </ul>
            ]]></field>
            <field name="required_experience"><![CDATA[
                <ul>
                    <li>Minimum 7 years of HR experience with at least 3 years in management role</li>
                    <li>Experience in recruitment, employee relations, and performance management</li>
                    <li>Knowledge of labor laws and HR best practices</li>
                    <li>Experience with HRIS systems and HR analytics</li>
                </ul>
            ]]></field>
            <field name="notes">This role requires a strategic thinker who can balance employee needs with business objectives while maintaining the highest standards of professionalism.</field>
        </record>

    </data>
</odoo>
