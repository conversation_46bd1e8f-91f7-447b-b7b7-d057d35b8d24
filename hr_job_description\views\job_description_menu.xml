<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Job Description Menu Item -->
        <menuitem id="menu_job_description"
                  name="Job Descriptions"
                  parent="hr.menu_hr_root"
                  action="action_job_description"
                  sequence="25"
                  groups="hr.group_hr_user"/>

        <!-- Submenu for Active Job Descriptions -->
        <record id="action_job_description_active" model="ir.actions.act_window">
            <field name="name">Active Job Descriptions</field>
            <field name="res_model">hr.job.description</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'active')]</field>
            <field name="context">{'default_state': 'active'}</field>
            <field name="search_view_id" ref="view_job_description_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No active job descriptions found
                </p>
                <p>
                    Create job descriptions to define roles and responsibilities
                    for your employees. Active job descriptions are currently
                    in use and represent the official job requirements.
                </p>
            </field>
        </record>

        <!-- Submenu for All Job Descriptions -->
        <record id="action_job_description_all" model="ir.actions.act_window">
            <field name="name">All Job Descriptions</field>
            <field name="res_model">hr.job.description</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_job_description_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first job description
                </p>
                <p>
                    Job descriptions help define roles, responsibilities, and requirements
                    for each position in your organization. Create comprehensive job
                    descriptions to improve clarity and performance management.
                </p>
            </field>
        </record>

        <!-- Submenu Items -->
        <menuitem id="menu_job_description_active"
                  name="Active Job Descriptions"
                  parent="menu_job_description"
                  action="action_job_description_active"
                  sequence="10"
                  groups="hr.group_hr_user"/>

        <menuitem id="menu_job_description_all"
                  name="All Job Descriptions"
                  parent="menu_job_description"
                  action="action_job_description_all"
                  sequence="20"
                  groups="hr.group_hr_user"/>

    </data>
</odoo>
