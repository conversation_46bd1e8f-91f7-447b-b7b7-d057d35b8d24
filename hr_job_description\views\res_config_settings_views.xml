<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit HR Settings to add Job Description configuration -->
        <record id="res_config_settings_view_form_inherit_job_description" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.job.description</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="hr.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@data-key='hr']" position="inside">
                    <h2>Job Descriptions</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="job_description_auto_approval"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="job_description_auto_approval"/>
                                <div class="text-muted">
                                    Automatically approve job descriptions when they are created
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="job_description_reminder_enabled"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="job_description_reminder_enabled"/>
                                <div class="text-muted">
                                    Send reminders for job descriptions pending approval
                                </div>
                                <div class="content-group" attrs="{'invisible': [('job_description_reminder_enabled', '=', False)]}">
                                    <div class="mt16">
                                        <label for="job_description_reminder_days" string="Reminder after"/>
                                        <field name="job_description_reminder_days" class="oe_inline"/> days
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
