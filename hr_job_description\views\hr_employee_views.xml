<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Employee Form View to Add Job Description Tab -->
        <record id="view_employee_form_job_description" model="ir.ui.view">
            <field name="name">hr.employee.form.job.description</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">

                <!-- Add Smart Buttons -->
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="action_view_job_descriptions" type="object"
                            class="oe_stat_button" icon="fa-file-text-o"
                            attrs="{'invisible': [('job_description_count', '=', 0)]}">
                        <field name="job_description_count" widget="statinfo"
                               string="Job Descriptions"/>
                    </button>
                    <button name="action_view_active_job_description" type="object"
                            class="oe_stat_button" icon="fa-file-text"
                            attrs="{'invisible': [('active_job_description_id', '=', False)]}">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Active</span>
                            <span class="o_stat_text">Job Description</span>
                        </div>
                    </button>
                </xpath>

                <!-- Add the Job Description tab after HR Settings tab -->
                <xpath expr="//page[@name='hr_settings']" position="after">
                    <page name="job_description" string="Job Description" groups="hr.group_hr_user">
                        <div class="row">
                            <div class="col-12">
                                <div class="o_job_description_header mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Job Description Management</h3>
                                        <button name="action_create_job_description" type="object"
                                                string="Create Job Description"
                                                class="btn btn-primary"
                                                groups="hr.group_hr_user"/>
                                    </div>
                                    <p class="text-muted mt-2">
                                        Manage comprehensive job descriptions including tasks, responsibilities,
                                        and requirements for this employee's position.
                                    </p>
                                </div>

                                <!-- Active Job Description Card -->
                                <div class="card mb-3" attrs="{'invisible': [('active_job_description_id', '=', False)]}">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fa fa-check-circle mr-2"/>
                                            Active Job Description
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>Title:</strong>
                                                <field name="active_job_description_id" readonly="1"
                                                       options="{'no_open': True}"/>
                                            </div>
                                            <div class="col-md-6 text-right">
                                                <button name="action_view_active_job_description" type="object"
                                                        string="View Details" class="btn btn-outline-primary btn-sm"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- No Job Description Message -->
                                <div class="alert alert-info" attrs="{'invisible': [('has_job_description', '=', True)]}">
                                    <h4 class="alert-heading">
                                        <i class="fa fa-info-circle mr-2"/>
                                        No Job Description Found
                                    </h4>
                                    <p class="mb-2">
                                        This employee doesn't have a job description yet.
                                        Create one to define their role, responsibilities, and requirements.
                                    </p>
                                    <hr/>
                                    <button name="action_create_job_description" type="object"
                                            string="Create First Job Description"
                                            class="btn btn-primary"/>
                                </div>

                                <!-- All Job Descriptions List -->
                                <div attrs="{'invisible': [('job_description_count', '=', 0)]}">
                                    <h4 class="mt-4 mb-3">All Job Descriptions</h4>
                                    <field name="job_description_ids" nolabel="1">
                                        <tree string="Job Descriptions"
                                              decoration-success="state == 'active'"
                                              decoration-muted="state == 'inactive'">
                                            <field name="name"/>
                                            <field name="job_title"/>
                                            <field name="approval_date"/>
                                            <field name="state" widget="badge"
                                                   decoration-success="state == 'active'"
                                                   decoration-muted="state == 'inactive'"/>
                                            <button name="action_activate" type="object"
                                                    string="Activate" icon="fa-check"
                                                    attrs="{'invisible': [('state', '=', 'active')]}"/>
                                            <button name="action_deactivate" type="object"
                                                    string="Deactivate" icon="fa-times"
                                                    attrs="{'invisible': [('state', '=', 'inactive')]}"/>
                                        </tree>
                                    </field>
                                </div>

                                <!-- Quick Actions -->
                                <div class="mt-4" attrs="{'invisible': [('job_description_count', '=', 0)]}">
                                    <h5>Quick Actions</h5>
                                    <div class="btn-group" role="group">
                                        <button name="action_view_job_descriptions" type="object"
                                                string="View All" class="btn btn-outline-secondary"/>
                                        <button name="action_create_job_description" type="object"
                                                string="Create New" class="btn btn-outline-primary"/>
                                        <button name="%(hr_job_description.action_job_description_report)d" type="action"
                                                string="Print Active" class="btn btn-outline-success"
                                                context="{'active_ids': [active_job_description_id]}"
                                                attrs="{'invisible': [('active_job_description_id', '=', False)]}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </page>
                </xpath>

                <!-- Add computed fields to the form (invisible) -->
                <xpath expr="//field[@name='company_id']" position="after">
                    <field name="job_description_count" invisible="1"/>
                    <field name="has_job_description" invisible="1"/>
                    <field name="active_job_description_id" invisible="1"/>
                </xpath>

            </field>
        </record>

        <!-- Inherit Employee Tree View to show job description info -->
        <record id="view_employee_tree_job_description" model="ir.ui.view">
            <field name="name">hr.employee.tree.job.description</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='job_id']" position="after">
                    <field name="has_job_description" string="Has Job Desc." optional="hide"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
