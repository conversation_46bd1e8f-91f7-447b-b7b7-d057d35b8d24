/* HR Job Description Module Styles */

.o_job_description_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    
    h3 {
        color: white;
        margin-bottom: 0;
    }
    
    p {
        margin-bottom: 0;
        opacity: 0.9;
    }
}

.o_job_description_card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-bottom: 20px;
    
    .card-header {
        border-radius: 10px 10px 0 0;
        border: none;
        
        &.bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        }
        
        h5 {
            margin-bottom: 0;
            
            i {
                margin-right: 8px;
            }
        }
    }
    
    .card-body {
        padding: 20px;
    }
}

.o_job_description_alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    
    .alert-heading {
        i {
            margin-right: 8px;
        }
    }
}

.o_job_description_actions {
    .btn-group {
        .btn {
            margin-right: 5px;
            border-radius: 5px;
            
            &:last-child {
                margin-right: 0;
            }
        }
    }
}

/* Form View Enhancements */
.o_form_view {
    .o_job_description_form {
        .oe_title h1 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
        }
        
        .o_notebook {
            .nav-tabs {
                border-bottom: 2px solid #2E86AB;
                
                .nav-link {
                    color: #666;
                    border: none;
                    border-radius: 0;
                    
                    &.active {
                        color: #2E86AB;
                        border-bottom: 3px solid #2E86AB;
                        background: none;
                    }
                    
                    &:hover {
                        color: #2E86AB;
                        border: none;
                    }
                }
            }
            
            .tab-content {
                padding: 20px 0;
            }
        }
        
        .o_group {
            .o_form_label {
                font-weight: 600;
                color: #333;
            }
        }
    }
}

/* Tree View Enhancements */
.o_list_view {
    .o_job_description_tree {
        .o_data_row {
            &:hover {
                background-color: #f8f9fa;
            }
            
            &.text-muted {
                opacity: 0.7;
            }
        }
        
        .badge {
            font-size: 0.8em;
            padding: 4px 8px;
            
            &.badge-success {
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            }
            
            &.badge-secondary {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            }
        }
    }
}

/* Button Enhancements */
.btn {
    &.btn-primary {
        background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
        border: none;
        border-radius: 5px;
        
        &:hover {
            background: linear-gradient(135deg, #A23B72 0%, #2E86AB 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
    }
    
    &.btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        
        &:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
        }
    }
    
    &.btn-outline-primary {
        border-color: #2E86AB;
        color: #2E86AB;
        
        &:hover {
            background: #2E86AB;
            border-color: #2E86AB;
        }
    }
    
    &.btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        
        &:hover {
            background: #6c757d;
            border-color: #6c757d;
        }
    }
    
    &.btn-outline-success {
        border-color: #28a745;
        color: #28a745;
        
        &:hover {
            background: #28a745;
            border-color: #28a745;
        }
    }
}

/* Stat Button Enhancements */
.oe_stat_button {
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .o_stat_info {
        .o_stat_text {
            font-weight: 600;
        }
    }
}

/* Rich Text Editor Enhancements */
.note-editor {
    border-radius: 8px;
    border: 1px solid #ddd;
    
    .note-toolbar {
        background: #f8f9fa;
        border-bottom: 1px solid #ddd;
        border-radius: 8px 8px 0 0;
    }
    
    .note-editing-area {
        border-radius: 0 0 8px 8px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .o_job_description_header {
        padding: 15px;
        
        h3 {
            font-size: 1.2em;
        }
        
        p {
            font-size: 0.9em;
        }
    }
    
    .o_job_description_actions {
        .btn-group {
            display: flex;
            flex-direction: column;
            
            .btn {
                margin-bottom: 5px;
                margin-right: 0;
            }
        }
    }
    
    .card {
        margin-bottom: 15px;
    }
}

/* Print Styles */
@media print {
    .o_job_description_header,
    .o_job_description_actions,
    .btn {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
